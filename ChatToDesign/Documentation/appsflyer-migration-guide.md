# AppsFlyer 架构重构迁移指南

## 迁移概述

本文档指导如何从旧的 `AppsFlyerDeepLinkHandler` 架构迁移到新的 `DeepLinkService` 协议架构。

## 当前状态

### ✅ 已完成的重构

1. **创建了新的协议和服务**
   - `DeepLinkService` 协议 (`ChatToDesign/Infrastructure/Services/DeepLink/DeepLinkService.swift`)
   - `AppsFlyerDeepLinkService` 实现 (`ChatToDesign/Infrastructure/Services/DeepLink/AppsFlyerDeepLinkService.swift`)

2. **简化了 AppsFlyerAnalyticsAdapter**
   - 移除了深度链接相关逻辑
   - 添加了依赖注入支持
   - 专注于分析事件处理

3. **更新了 AppDependencyContainer**
   - 添加了依赖注入逻辑
   - 保持向后兼容性

4. **更新了应用入口点**
   - 添加了迁移注释
   - 保持现有功能正常工作

### 🔄 待完成的迁移步骤

## 迁移步骤

### 步骤 1: 将新文件添加到 Xcode 项目

1. 打开 Xcode 项目
2. 在项目导航器中找到 `ChatToDesign/Infrastructure/Services/` 目录
3. 如果 `DeepLink` 目录不存在，创建它
4. 将以下文件添加到项目中：
   - `DeepLinkService.swift`
   - `AppsFlyerDeepLinkService.swift`
5. 确保文件被添加到正确的 target

### 步骤 2: 更新 AppDependencyContainer

在 `ChatToDesign/Infrastructure/DI/AppDependencyContainer.swift` 中：

```swift
// 1. 更新属性声明
/// 深度链接服务
public let deepLinkService: DeepLinkService

// 2. 更新初始化逻辑
private init() {
    // ... 其他初始化代码 ...
    
    // 创建深度链接服务 (先创建，用于注入)
    let appsFlyerDeepLinkService = AppsFlyerDeepLinkService()
    self.deepLinkService = appsFlyerDeepLinkService
    
    // 创建 AppsFlyer 分析服务并注入深度链接服务
    let appsFlyerAnalyticsAdapter = AppsFlyerAnalyticsAdapter()
    self.attributionAnalyticsService = appsFlyerAnalyticsAdapter
    
    // ... 其他初始化代码 ...
}
```

### 步骤 3: 更新应用入口点

在 `ChatToDesignApp.swift` 中：

```swift
.onOpenURL { url in
    let container = AppDependencyContainer.shared
    _ = container.deepLinkService.handleURL(url)
}
.onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
    let container = AppDependencyContainer.shared
    _ = container.deepLinkService.handleUniversalLink(userActivity)
}

// 在 scenePhase 处理中
case .active:
    Logger.info("App: Scene became active")
    let container = AppDependencyContainer.shared
    container.deepLinkService.handleAppBecameActive()
```

在 `AppDelegate.swift` 中：

```swift
func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey: Any] = [:]
) -> Bool {
    let container = AppDependencyContainer.shared
    return container.deepLinkService.handleURL(url)
}
```

### 步骤 4: 移除旧代码

1. **删除旧文件**：
   - `ChatToDesign/Infrastructure/ThirdParty/AppsFlyer/AppsFlyerDeepLinkHandler.swift`

2. **清理 AppDependencyContainer**：
   - 移除 `deepLinkHandler` 属性
   - 移除相关的 TODO 注释

3. **清理应用入口点**：
   - 移除 TODO 注释
   - 确保所有调用都使用新的 `deepLinkService`

### 步骤 5: 验证功能

1. **编译测试**：
   ```bash
   # 在项目根目录执行
   xcodebuild -scheme ChatToDesign -configuration Debug build
   ```

2. **深度链接测试**：
   ```bash
   # 使用测试脚本
   ./ChatToDesign/Documentation/test-deep-links.sh
   ```

3. **功能验证**：
   - URL Scheme 深度链接
   - Universal Links（需要 AASA 配置）
   - 应用生命周期处理
   - 路由通知系统

## 架构对比

### 旧架构问题

```
AppDependencyContainer
├── attributionAnalyticsService: AppsFlyerAnalyticsAdapter
└── deepLinkHandler: AppsFlyerDeepLinkHandler

AppsFlyerAnalyticsAdapter
├── 配置 AppsFlyer SDK
├── 处理分析事件
└── 设置 deepLinkDelegate = container.deepLinkHandler  ❌ 循环依赖
```

### 新架构优势

```
AppDependencyContainer
├── attributionAnalyticsService: AppsFlyerAnalyticsAdapter
└── deepLinkService: AppsFlyerDeepLinkService

AppsFlyerAnalyticsAdapter
├── 配置 AppsFlyer SDK
├── 处理分析事件
└── 接收注入的 deepLinkService  ✅ 清晰依赖

AppsFlyerDeepLinkService
├── 实现 DeepLinkService 协议
├── 实现 DeepLinkDelegate 协议
└── 专注深度链接处理  ✅ 单一职责
```

## 测试清单

### 编译测试
- [ ] 项目编译无错误
- [ ] 所有导入正确解析
- [ ] 协议实现完整

### 功能测试
- [ ] URL Scheme 深度链接正常工作
- [ ] Universal Links 正常工作（需要 AASA 配置）
- [ ] 应用生命周期处理正常
- [ ] 路由通知系统正常
- [ ] AppsFlyer SDK 正常初始化

### 集成测试
- [ ] 深度链接参数正确传递
- [ ] 路由到正确的页面
- [ ] 分析事件正常追踪
- [ ] 错误处理正常

## 回滚计划

如果迁移过程中遇到问题，可以按以下步骤回滚：

1. **恢复 AppDependencyContainer**：
   ```swift
   public let deepLinkHandler: AppsFlyerDeepLinkHandler
   ```

2. **恢复应用入口点**：
   ```swift
   container.deepLinkHandler.handleURL(url)
   ```

3. **恢复 AppsFlyerAnalyticsAdapter**：
   ```swift
   AppsFlyerLib.shared().deepLinkDelegate = container.deepLinkHandler
   ```

## 性能影响

### 内存使用
- **减少**: 移除了循环依赖，减少内存泄漏风险
- **优化**: 更清晰的对象生命周期管理

### 启动时间
- **无影响**: 依赖注入在启动时完成，不影响运行时性能
- **优化**: 更少的运行时依赖查找

### 代码维护
- **改善**: 更清晰的职责分离
- **改善**: 更好的测试能力
- **改善**: 更容易扩展和修改

## 总结

这次重构解决了以下关键问题：

1. **循环依赖**: 通过依赖注入消除了循环依赖
2. **职责混乱**: 清晰分离了分析和深度链接职责
3. **架构一致性**: 使用协议抽象，与项目其他部分保持一致
4. **可测试性**: 每个组件都可以独立测试

重构后的架构更加健壮、可维护，并为未来的扩展提供了良好的基础。
