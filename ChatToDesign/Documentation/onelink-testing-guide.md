# AppsFlyer OneLink 测试指南

## 概述

本文档提供了 AppsFlyer OneLink 深度链接的完整测试指南，包括控制台配置、测试链接创建和验证步骤。

## 前置条件

### 1. AppsFlyer 控制台配置

#### 应用基本信息
- **应用名称**: ChatToDesign
- **Bundle ID**: `com.a1d.chat-to-design`
- **平台**: iOS
- **Team ID**: 需要从 Apple Developer 账户获取

#### OneLink 域名
- **域名**: `picadabra-ai.onelink.me`
- **状态**: 已配置但 AASA 文件为空

### 2. 获取 Team ID

```bash
# 方法1: 从 Keychain Access 查看开发证书
# 方法2: 从 Apple Developer 账户获取
# 方法3: 从 Xcode 项目设置查看
```

## OneLink 模板配置

### 1. 创建 OneLink 模板

在 AppsFlyer 控制台中：

1. 导航到 **Engagement** > **OneLink**
2. 点击 **Create New OneLink**
3. 配置基本信息：
   - **Template Name**: ChatToDesign_Main
   - **Domain**: picadabra-ai.onelink.me
   - **App**: ChatToDesign (iOS)

### 2. 深度链接配置

#### 基础参数映射

| 参数名 | 描述 | 示例值 |
|--------|------|--------|
| `deep_link_value` | 目标页面标识 | `create`, `explore`, `profile` |
| `deep_link_sub1` | 具体ID | `template_id_123`, `chat_id_456` |
| `deep_link_sub2` | 额外参数 | `campaign_id`, `user_id` |
| `deep_link_sub3` | 推荐码 | `referral_code` |

#### 路由映射表

| deep_link_value | 目标页面 | Tab | 描述 |
|-----------------|----------|-----|------|
| `create` | CreatePageView | create | 创建页面 |
| `explore` | ExplorePageView | explore | 探索页面 |
| `profile` | ProfilePageView | profile | 个人资料 |
| `subscription` | PaywallView | - | 订阅页面 |
| `template` | 模板详情 | - | 特定模板 |
| `chat` | 聊天功能 | - | 特定聊天 |

## 测试链接示例

### 1. 基础路由测试

```
# 跳转到创建页面
https://picadabra-ai.onelink.me/{template_id}?pid=test&deep_link_value=create

# 跳转到探索页面
https://picadabra-ai.onelink.me/{template_id}?pid=test&deep_link_value=explore

# 跳转到个人资料
https://picadabra-ai.onelink.me/{template_id}?pid=test&deep_link_value=profile
```

### 2. 带参数的路由测试

```
# 跳转到特定模板
https://picadabra-ai.onelink.me/{template_id}?pid=test&deep_link_value=template&deep_link_sub1=video_template_001

# 跳转到特定聊天
https://picadabra-ai.onelink.me/{template_id}?pid=test&deep_link_value=chat&deep_link_sub1=chat_id_123

# 跳转到订阅页面（带营销参数）
https://picadabra-ai.onelink.me/{template_id}?pid=email&c=summer_sale&deep_link_value=subscription&deep_link_sub1=premium
```

### 3. 营销活动测试

```
# 邮件营销活动
https://picadabra-ai.onelink.me/{template_id}?pid=email&c=newsletter_2024&deep_link_value=create&deep_link_sub2=email_campaign

# 社交媒体活动
https://picadabra-ai.onelink.me/{template_id}?pid=facebook&c=social_promo&deep_link_value=explore&deep_link_sub2=fb_ad_001

# 推荐活动
https://picadabra-ai.onelink.me/{template_id}?pid=referral&deep_link_value=subscription&deep_link_sub3=user_referral_abc123
```

## 测试场景

### 1. 新用户安装流程

**步骤**:
1. 确保设备上未安装应用
2. 点击深度链接
3. 重定向到 App Store
4. 安装应用
5. 首次启动应用
6. 验证延迟深度链接是否正确路由

**预期结果**:
- 应用启动后自动导航到指定页面
- 深度链接参数正确传递

### 2. 现有用户流程

**步骤**:
1. 确保应用已安装
2. 点击深度链接
3. 应用直接打开

**预期结果**:
- 应用立即打开并导航到指定页面
- 即时深度链接处理

### 3. 应用在后台流程

**步骤**:
1. 应用在后台运行
2. 点击深度链接
3. 应用切换到前台

**预期结果**:
- 应用前台显示并导航到指定页面

## 调试和验证

### 1. 日志监控

在 Xcode 控制台中查看以下日志：

```
# AppsFlyer SDK 初始化
🚀 AppsFlyer: Starting SDK

# 深度链接接收
AppsFlyerAnalyticsAdapter: UDL Deep link resolved with status: found

# 路由处理
AppsFlyerDeepLinkHandler: UDL Deep link found - isDeferred: false
AppsFlyerDeepLinkHandler: Using UDL deeplinkValue: create
AppsFlyerDeepLinkHandler: Routing to create

# Tab 切换
MainTabView: Deep link routing to create tab
```

### 2. 参数验证

验证深度链接参数是否正确传递：

```swift
// 在 AppsFlyerDeepLinkHandler 中添加调试日志
Logger.info("Deep link parameters: \(parameters.allParameters)")
```

### 3. 错误处理

常见错误和解决方案：

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| AASA 文件为空 | AppsFlyer 配置未完成 | 完成控制台配置 |
| 深度链接不工作 | Bundle ID 不匹配 | 检查配置一致性 |
| 参数丢失 | 参数映射错误 | 检查参数名称 |

## 性能监控

### 1. 关键指标

- 深度链接点击率
- 安装转化率
- 深度链接成功率
- 用户路径分析

### 2. 事件追踪

```swift
// 深度链接事件追踪
trackDeepLinkEvent(parameters: parameters.allParameters, status: "udl_found")
```

## 下一步行动

1. **立即执行**: 在 AppsFlyer 控制台创建 OneLink 模板
2. **配置 AASA**: 确保 AASA 文件包含正确的应用信息
3. **创建测试链接**: 使用上述示例创建测试链接
4. **执行测试**: 按照测试场景验证功能
5. **监控和优化**: 根据测试结果优化配置

## 注意事项

- Universal Links 在模拟器中可能不工作，建议使用真机测试
- AASA 文件更新可能需要时间生效
- 首次安装后的延迟深度链接可能需要几秒钟处理时间
- 确保网络连接稳定以获得最佳测试结果
