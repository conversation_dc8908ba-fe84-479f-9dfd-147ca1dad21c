//
//  DeepLinkService.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/21.
//

import Foundation
import Combine

/// Deep link service protocol defining standard interface for deep link handling
public protocol DeepLinkService: AnyObject {
    
    // MARK: - State Properties
    
    /// Indicates if a deep link is currently being processed
    var isProcessingDeepLink: Bool { get }
    
    /// Deep link data received from the service
    var deepLinkData: [String: Any]? { get }
    
    // MARK: - URL Handling
    
    /// Handle URL schemes (custom app URLs)
    /// - Parameter url: The URL to handle
    /// - Returns: True if the URL was handled successfully
    func handleURL(_ url: URL) -> Bool
    
    /// Handle Universal Links (web URLs that open the app)
    /// - Parameter userActivity: The user activity containing the web URL
    /// - Returns: True if the Universal Link was handled successfully
    func handleUniversalLink(_ userActivity: NSUserActivity) -> Bool
    
    // MARK: - Lifecycle Management
    
    /// Handle app becoming active - perform any necessary initialization
    func handleAppBecameActive()
    
    /// Handle app moving to background - perform cleanup if needed
    func handleAppMovedToBackground()
    
    // MARK: - Configuration
    
    /// Configure the deep link service with necessary parameters
    /// - Parameter configuration: Service-specific configuration
    func configure(with configuration: DeepLinkConfiguration) async throws
    
    /// Check if the service is properly configured
    var isConfigured: Bool { get }
}

// MARK: - Deep Link Configuration

/// Configuration for deep link services
public struct DeepLinkConfiguration {
    /// Whether deep linking is enabled
    public let enabled: Bool
    
    /// Debug mode for additional logging
    public let debugMode: Bool
    
    /// Custom parameters for service-specific configuration
    public let customParameters: [String: Any]
    
    /// Default configuration
    public static let `default` = DeepLinkConfiguration(
        enabled: true,
        debugMode: false,
        customParameters: [:]
    )
    
    public init(enabled: Bool = true, debugMode: Bool = false, customParameters: [String: Any] = [:]) {
        self.enabled = enabled
        self.debugMode = debugMode
        self.customParameters = customParameters
    }
}

// MARK: - Deep Link Events

/// Deep link event types for notification system
public enum DeepLinkEvent {
    case urlReceived(URL)
    case universalLinkReceived(URL)
    case processingStarted
    case processingCompleted(success: Bool)
    case routingCompleted(destination: String)
    case error(Error)
}

// MARK: - Deep Link Parameters

/// Standard deep link parameters structure
public class DeepLinkParameters {
    // Marketing parameters
    public var campaign: String?
    public var mediaSource: String?
    public var channel: String?
    public var keywords: String?
    public var adGroup: String?
    public var adSet: String?
    public var ad: String?
    
    // Routing parameters
    public var deepLinkValue: String?
    public var deepLinkSubValue1: String?
    public var deepLinkSubValue2: String?
    public var deepLinkSubValue3: String?
    
    // Custom parameters
    public var customParameters: [String: Any] = [:]
    
    /// All parameters combined for tracking
    public var allParameters: [String: Any] {
        var params: [String: Any] = customParameters
        
        if let campaign = campaign { params["campaign"] = campaign }
        if let mediaSource = mediaSource { params["media_source"] = mediaSource }
        if let channel = channel { params["channel"] = channel }
        if let keywords = keywords { params["keywords"] = keywords }
        if let adGroup = adGroup { params["adgroup"] = adGroup }
        if let adSet = adSet { params["adset"] = adSet }
        if let ad = ad { params["ad"] = ad }
        
        if let deepLinkValue = deepLinkValue { params["deep_link_value"] = deepLinkValue }
        if let deepLinkSubValue1 = deepLinkSubValue1 { params["deep_link_sub1"] = deepLinkSubValue1 }
        if let deepLinkSubValue2 = deepLinkSubValue2 { params["deep_link_sub2"] = deepLinkSubValue2 }
        if let deepLinkSubValue3 = deepLinkSubValue3 { params["deep_link_sub3"] = deepLinkSubValue3 }
        
        return params
    }
    
    public init() {}
}

// MARK: - Deep Link Errors

/// Errors that can occur during deep link processing
public enum DeepLinkError: Error, LocalizedError {
    case notConfigured
    case invalidURL(String)
    case processingFailed(String)
    case routingFailed(String)
    case serviceUnavailable
    
    public var errorDescription: String? {
        switch self {
        case .notConfigured:
            return "Deep link service is not configured"
        case .invalidURL(let url):
            return "Invalid URL: \(url)"
        case .processingFailed(let reason):
            return "Deep link processing failed: \(reason)"
        case .routingFailed(let reason):
            return "Deep link routing failed: \(reason)"
        case .serviceUnavailable:
            return "Deep link service is unavailable"
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    // Deep link routing notifications
    public static let deepLinkRouteToChat = Notification.Name("DeepLinkRouteToChat")
    public static let deepLinkRouteToDesign = Notification.Name("DeepLinkRouteToDesign")
    public static let deepLinkRouteToProfile = Notification.Name("DeepLinkRouteToProfile")
    public static let deepLinkRouteToSubscription = Notification.Name("DeepLinkRouteToSubscription")
    public static let deepLinkRouteToOnboarding = Notification.Name("DeepLinkRouteToOnboarding")
    public static let deepLinkRouteToDefault = Notification.Name("DeepLinkRouteToDefault")
    
    // New OneLink routing notifications
    public static let deepLinkRouteToExplore = Notification.Name("DeepLinkRouteToExplore")
    public static let deepLinkRouteToCreate = Notification.Name("DeepLinkRouteToCreate")
    public static let deepLinkRouteToTemplate = Notification.Name("DeepLinkRouteToTemplate")
    public static let deepLinkRouteToSpecificChat = Notification.Name("DeepLinkRouteToSpecificChat")
    
    // Deep link event tracking
    public static let deepLinkEventTracked = Notification.Name("DeepLinkEventTracked")
    
    // AppsFlyer specific notifications (for backward compatibility)
    public static let appsFlyerDeepLinkReceived = Notification.Name("AppsFlyerDeepLinkReceived")
}
